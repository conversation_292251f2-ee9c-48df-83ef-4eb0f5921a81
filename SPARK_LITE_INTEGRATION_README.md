# Spark Lite 模型集成说明

本文档说明如何在AI对冲基金回测系统中使用新集成的讯飞星火Lite模型。

## 概述

Spark Lite是讯飞星火认知大模型的轻量版本，具有以下特点：
- 快速响应
- 成本效益高
- 支持中文对话
- 适合金融分析场景

## 配置步骤

### 1. 获取API凭据

1. 访问 [讯飞开放平台](https://console.xfyun.cn/services/bmx1)
2. 注册账号并创建应用
3. 开通星火认知大模型服务
4. 获取以下凭据：
   - APP_ID
   - API_KEY
   - API_SECRET

### 2. 配置环境变量

在项目根目录的 `.env` 文件中添加以下配置：

```bash
# 讯飞星火 Lite 模型配置
SPARK_APP_ID=your_app_id_here
SPARK_API_KEY=your_api_key_here
SPARK_API_SECRET=your_api_secret_here
```

### 3. 安装依赖

确保已安装websocket-client依赖：

```bash
poetry add websocket-client
```

## 使用方法

### 在回测系统中使用

1. 启动回测系统：
   ```bash
   python src/backtester.py
   ```

2. 在模型选择界面中选择：
   ```
   [spark] spark-lite
   ```

3. 系统将使用Spark Lite模型进行分析

### 在主系统中使用

1. 启动主系统：
   ```bash
   python src/main.py
   ```

2. 选择Spark Lite模型进行交易决策

## 测试集成

运行测试脚本验证集成是否成功：

```bash
python test_spark_lite_integration.py
```

测试将验证：
- 模型信息获取
- 凭据配置
- 模型创建
- 简单对话
- 金融问题回答

## 技术实现

### 文件结构

```
src/llm/
├── models.py              # 模型提供商和配置
├── api_models.json        # API模型列表
└── spark_lite.py          # Spark Lite LangChain包装器
```

### 核心组件

1. **ChatSparkLite类**: LangChain兼容的Spark Lite包装器
2. **WebSocket连接**: 实现与讯飞星火API的实时通信
3. **消息转换**: 将LangChain消息格式转换为Spark API格式

### 特性支持

- ✅ 同步对话
- ✅ 消息历史
- ✅ 错误处理
- ✅ 超时控制
- ✅ 推理内容支持
- ✅ 温度参数控制

## 注意事项

### 安全性
- 请妥善保管API凭据
- 不要将凭据提交到版本控制系统
- 建议使用环境变量管理敏感信息

### 性能
- Spark Lite适合轻量级分析任务
- 对于复杂分析，建议使用更高级的模型
- 注意API调用频率限制

### 网络
- 需要稳定的网络连接
- WebSocket连接可能受防火墙影响
- 建议在生产环境中配置重试机制

## 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证API凭据
   - 确认服务状态

2. **认证错误**
   - 检查APP_ID、API_KEY、API_SECRET
   - 确认应用已开通服务
   - 检查凭据格式

3. **响应超时**
   - 检查网络稳定性
   - 调整超时参数
   - 重试请求

### 调试模式

启用WebSocket调试：

```python
import websocket
websocket.enableTrace(True)
```

## 更新日志

### v1.0.0 (2025-06-19)
- 初始集成Spark Lite模型
- 实现LangChain兼容接口
- 添加WebSocket通信支持
- 集成到回测系统

## 支持

如有问题，请：
1. 检查本文档的故障排除部分
2. 运行测试脚本诊断问题
3. 查看讯飞开放平台文档
4. 联系技术支持

## 参考资料

- [讯飞开放平台](https://www.xfyun.cn/)
- [星火认知大模型文档](https://www.xfyun.cn/doc/spark/X1ws.html)
- [LangChain文档](https://python.langchain.com/)
- [WebSocket客户端文档](https://websocket-client.readthedocs.io/)
