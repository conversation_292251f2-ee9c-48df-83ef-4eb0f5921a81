#!/usr/bin/env python3
"""
Spark Lite 回测演示脚本

演示如何在AI对冲基金系统中使用Spark Lite模型进行回测
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from src.backtester import Backtester
from src.main import run_hedge_fund

def demo_spark_lite_backtesting():
    """演示使用Spark Lite进行回测"""
    print("🚀 Spark Lite 回测演示")
    print("=" * 50)
    
    # 检查环境变量
    required_vars = ["SPARK_APP_ID", "SPARK_API_KEY", "SPARK_API_SECRET"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("❌ 缺少必要的环境变量:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n请在.env文件中配置Spark Lite凭据")
        print("参考 spark_lite_env_example.txt 文件")
        return False
    
    print("✅ Spark Lite 凭据配置完整")
    
    # 回测参数
    tickers = ["AAPL"]  # 测试股票
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
    initial_capital = 100000.0
    
    print(f"\n📊 回测参数:")
    print(f"   股票代码: {', '.join(tickers)}")
    print(f"   开始日期: {start_date}")
    print(f"   结束日期: {end_date}")
    print(f"   初始资金: ${initial_capital:,.2f}")
    print(f"   模型: Spark Lite")
    
    try:
        # 创建回测器
        backtester = Backtester(
            agent=run_hedge_fund,
            tickers=tickers,
            start_date=start_date,
            end_date=end_date,
            initial_capital=initial_capital,
            model_name="spark-lite",
            model_provider="SparkLite",
            selected_analysts=["warren_buffett", "peter_lynch"],  # 选择分析师
            show_reasoning=True,
            save_reasoning=True,
        )
        
        print("\n🔄 开始回测...")
        results = backtester.run()
        
        print("\n📈 回测完成!")
        print(f"   最终资产价值: ${results['final_portfolio_value']:,.2f}")
        print(f"   总收益: ${results['total_return']:,.2f}")
        print(f"   收益率: {results['total_return_pct']:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 回测失败: {str(e)}")
        return False

def demo_spark_lite_model_selection():
    """演示Spark Lite模型选择"""
    print("\n🎯 Spark Lite 模型选择演示")
    print("=" * 50)
    
    from src.llm.models import LLM_ORDER, get_model_info
    
    print("📋 可用模型列表:")
    for i, (display_name, model_name, provider) in enumerate(LLM_ORDER, 1):
        if "spark" in display_name.lower():
            print(f"   {i}. {display_name}")
            model_info = get_model_info(model_name, provider)
            if model_info:
                print(f"      模型名称: {model_info.model_name}")
                print(f"      提供商: {model_info.provider}")
    
    print("\n💡 使用说明:")
    print("1. 在回测系统中选择 '[spark] spark-lite'")
    print("2. 系统将使用讯飞星火Lite模型进行分析")
    print("3. 支持中文对话和金融分析")

def main():
    """主函数"""
    print("🌟 Spark Lite AI对冲基金演示")
    print("=" * 60)
    
    # 演示模型选择
    demo_spark_lite_model_selection()
    
    # 询问是否运行回测演示
    print("\n" + "=" * 60)
    response = input("是否运行回测演示? (需要配置API凭据) [y/N]: ").strip().lower()
    
    if response in ['y', 'yes', '是']:
        success = demo_spark_lite_backtesting()
        if success:
            print("\n🎉 演示完成!")
        else:
            print("\n⚠️ 演示未完成，请检查配置")
    else:
        print("\n📝 跳过回测演示")
        print("要运行完整演示，请:")
        print("1. 配置Spark Lite API凭据")
        print("2. 重新运行此脚本并选择 'y'")
    
    print("\n📚 更多信息请参考:")
    print("   - SPARK_LITE_INTEGRATION_README.md")
    print("   - spark_lite_env_example.txt")

if __name__ == "__main__":
    main()
