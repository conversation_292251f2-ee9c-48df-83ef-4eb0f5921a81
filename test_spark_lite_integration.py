#!/usr/bin/env python3
"""
Spark Lite集成测试脚本

测试新添加的Spark Lite模型是否能正确集成到回测系统中
"""

import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

from src.llm.models import get_model, ModelProvider, get_model_info
from src.llm.spark_lite import ChatSparkLite
from langchain_core.messages import HumanMessage

# 加载环境变量
load_dotenv()

def test_spark_lite_model_info():
    """测试Spark Lite模型信息获取"""
    print("🔍 测试Spark Lite模型信息获取...")
    
    model_info = get_model_info("spark-lite", "SparkLite")
    if model_info:
        print(f"✅ 模型信息获取成功:")
        print(f"   显示名称: {model_info.display_name}")
        print(f"   模型名称: {model_info.model_name}")
        print(f"   提供商: {model_info.provider}")
        return True
    else:
        print("❌ 模型信息获取失败")
        return False

def test_spark_lite_credentials():
    """测试Spark Lite凭据配置"""
    print("\n🔑 测试Spark Lite凭据配置...")
    
    app_id = os.getenv("SPARK_APP_ID")
    api_key = os.getenv("SPARK_API_KEY")
    api_secret = os.getenv("SPARK_API_SECRET")
    
    if all([app_id, api_key, api_secret]):
        print("✅ Spark Lite凭据配置完整")
        print(f"   APP_ID: {app_id[:8]}...")
        print(f"   API_KEY: {api_key[:8]}...")
        print(f"   API_SECRET: {api_secret[:8]}...")
        return True
    else:
        print("❌ Spark Lite凭据配置不完整")
        print("   请在.env文件中设置以下环境变量:")
        print("   - SPARK_APP_ID")
        print("   - SPARK_API_KEY")
        print("   - SPARK_API_SECRET")
        return False

def test_spark_lite_model_creation():
    """测试Spark Lite模型创建"""
    print("\n🏗️ 测试Spark Lite模型创建...")
    
    try:
        model = get_model("spark-lite", ModelProvider.SPARK_LITE)
        if isinstance(model, ChatSparkLite):
            print("✅ Spark Lite模型创建成功")
            print(f"   模型类型: {type(model).__name__}")
            print(f"   域名: {model.domain}")
            print(f"   温度: {model.temperature}")
            return True
        else:
            print(f"❌ 模型类型错误: {type(model).__name__}")
            return False
    except Exception as e:
        print(f"❌ 模型创建失败: {str(e)}")
        return False

def test_spark_lite_simple_chat():
    """测试Spark Lite简单对话"""
    print("\n💬 测试Spark Lite简单对话...")
    
    try:
        model = get_model("spark-lite", ModelProvider.SPARK_LITE)
        
        # 创建测试消息
        test_message = HumanMessage(content="你好，请简单介绍一下你自己。")
        
        print("   发送测试消息...")
        response = model.invoke([test_message])
        
        if response and response.content:
            print("✅ Spark Lite对话测试成功")
            print(f"   响应长度: {len(response.content)} 字符")
            print(f"   响应预览: {response.content[:100]}...")
            return True
        else:
            print("❌ 响应为空")
            return False
            
    except Exception as e:
        print(f"❌ 对话测试失败: {str(e)}")
        return False

def test_spark_lite_financial_question():
    """测试Spark Lite金融问题回答"""
    print("\n📈 测试Spark Lite金融问题回答...")
    
    try:
        model = get_model("spark-lite", ModelProvider.SPARK_LITE)
        
        # 创建金融相关测试消息
        financial_message = HumanMessage(
            content="请分析一下苹果公司(AAPL)的投资价值，考虑其财务状况和市场前景。"
        )
        
        print("   发送金融分析问题...")
        response = model.invoke([financial_message])
        
        if response and response.content:
            print("✅ Spark Lite金融分析测试成功")
            print(f"   响应长度: {len(response.content)} 字符")
            print(f"   响应预览: {response.content[:200]}...")
            return True
        else:
            print("❌ 金融分析响应为空")
            return False
            
    except Exception as e:
        print(f"❌ 金融分析测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Spark Lite集成测试\n")
    
    tests = [
        test_spark_lite_model_info,
        test_spark_lite_credentials,
        test_spark_lite_model_creation,
        test_spark_lite_simple_chat,
        test_spark_lite_financial_question,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Spark Lite已成功集成到回测系统中！")
        print("\n📝 使用说明:")
        print("1. 确保在.env文件中配置了Spark凭据")
        print("2. 在回测时选择 '[spark] spark-lite' 模型")
        print("3. 系统将使用讯飞星火Lite模型进行分析")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
